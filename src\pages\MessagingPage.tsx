
import { useState, useEffect } from "react";
import { 
  Send, 
  Search, 
  MoreVertical, 
  Phone, 
  Video, 
  Image, 
  Paperclip, 
  ChevronLeft, 
  Plus,
  UserPlus,
  Users,
  Settings
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar } from "@/components/ui/avatar";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { getFirestore, collection, getDocs, query, where, DocumentData } from "firebase/firestore";
import { useIsMobile } from "@/hooks/use-mobile";

type Contact = {
  id: string;
  name: string;
  position: string;
  status: "online" | "offline";
  lastSeen: string;
  unread?: number;
  role: "admin" | "shopkeeper";
};

type Message = {
  id: string;
  senderId: string;
  text: string;
  timestamp: string;
  sent: boolean;
};

const defaultMessages = [
  { id: "1", senderId: "1", text: "Hi there! I need help with today's inventory count.", timestamp: "09:00 AM", sent: false },
  { id: "2", senderId: "current", text: "Good morning John! I'll be available in about 30 minutes to help with that.", timestamp: "09:05 AM", sent: true },
  { id: "3", senderId: "1", text: "Perfect, thanks! I'll start preparing the forms.", timestamp: "09:07 AM", sent: false },
  { id: "4", senderId: "current", text: "Great! Make sure to include the new shipment that arrived yesterday.", timestamp: "09:10 AM", sent: true },
  { id: "5", senderId: "1", text: "Will do. I've already set aside those items for processing.", timestamp: "09:12 AM", sent: false },
  { id: "6", senderId: "current", text: "Excellent work. See you in a bit!", timestamp: "09:15 AM", sent: true },
];

export default function MessagingPage() {
  const { currentUser, isAdmin } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [messageText, setMessageText] = useState("");
  const [messages, setMessages] = useState<Message[]>(defaultMessages);
  const [showMobileConversation, setShowMobileConversation] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchContacts = async () => {
      setIsLoading(true);
      try {
        const db = getFirestore();
        let contactsQuery;
        
        if (!isAdmin()) {
          contactsQuery = query(
            collection(db, "users"),
            where("role", "==", "admin")
          );
        } else {
          contactsQuery = query(
            collection(db, "users"),
            where("role", "==", "shopkeeper")
          );
        }
        
        const querySnapshot = await getDocs(contactsQuery);
        const fetchedContacts: Contact[] = querySnapshot.docs.map((doc) => {
          const data = doc.data() as DocumentData;
          return {
            id: doc.id,
            name: data.displayName || (data.email as string)?.split('@')[0] || 'Unknown User',
            position: data.role === 'admin' ? 'Administrator' : 'Shop Staff',
            status: Math.random() > 0.5 ? "online" : "offline",
            lastSeen: Math.random() > 0.5 ? "Now" : `${Math.floor(Math.random() * 5) + 1}h ago`,
            role: data.role as "admin" | "shopkeeper",
            unread: Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : undefined,
          };
        });
        
        setContacts(fetchedContacts);
        
        if (fetchedContacts.length > 0 && !selectedContact) {
          setSelectedContact(fetchedContacts[0]);
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchContacts();
  }, [currentUser, isAdmin]);

  const filteredContacts = contacts.filter(contact => 
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.position.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSendMessage = () => {
    if (messageText.trim() === "" || !selectedContact) return;
    
    const newMessage = {
      id: Date.now().toString(),
      senderId: "current",
      text: messageText,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      sent: true,
    };
    
    setMessages([...messages, newMessage]);
    setMessageText("");
  };

  const handleSelectContact = (contact: Contact) => {
    setSelectedContact(contact);
    setShowMobileConversation(true);
  };

  const handleBackToContacts = () => {
    setShowMobileConversation(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  const formatMessageDate = (timestamp: string) => {
    return timestamp;
  };

  return (
    <div className="h-[calc(100vh-8rem)] flex flex-col animate-fade-in overflow-hidden">
      <div className="mb-4">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Messaging</h1>
        <p className="text-sm md:text-base text-muted-foreground">
          {isAdmin() 
            ? "Communicate with your shop staff." 
            : "Communicate with administration."}
        </p>
      </div>

      <Card className="flex flex-1 overflow-hidden border">
        <div className={`w-full md:w-1/3 border-r ${showMobileConversation ? 'hidden md:block' : 'block'}`}>
          <CardHeader className="p-3 md:p-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg md:text-xl">
                {isAdmin() ? "Shop Staff" : "Administration"}
              </CardTitle>
              {isAdmin() && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <UserPlus className="mr-2 h-4 w-4" />
                      New Contact
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Users className="mr-2 h-4 w-4" />
                      New Group
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      Settings
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
            {isAdmin() && (
              <div className="relative mt-2">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search contacts..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            )}
            {isAdmin() && (
              <Tabs defaultValue="all" className="mt-2">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="unread">Unread</TabsTrigger>
                </TabsList>
              </Tabs>
            )}
          </CardHeader>
          <Separator />
          <ScrollArea className="h-[calc(100%-128px)]">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredContacts.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <Users className="mx-auto h-12 w-12 mb-3" />
                <p>No contacts found</p>
                {searchTerm && <p className="text-sm mt-1">Try adjusting your search</p>}
              </div>
            ) : (
              <div className="p-2">
                {filteredContacts.map((contact) => (
                  <div
                    key={contact.id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedContact?.id === contact.id
                        ? "bg-muted"
                        : "hover:bg-muted/50"
                    }`}
                    onClick={() => handleSelectContact(contact)}
                  >
                    <div className="relative">
                      <Avatar className="h-10 w-10 border">
                        <div className="flex h-full w-full items-center justify-center bg-primary/10 text-primary">
                          {getInitials(contact.name)}
                        </div>
                      </Avatar>
                      {contact.status === "online" && (
                        <div className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-background"></div>
                      )}
                    </div>
                    <div className="ml-3 flex-1 overflow-hidden">
                      <div className="flex items-center justify-between">
                        <p className="font-medium truncate">{contact.name}</p>
                        <span className="text-xs text-muted-foreground">
                          {contact.lastSeen}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {contact.position}
                      </p>
                    </div>
                    {contact.unread && (
                      <Badge className="ml-2 bg-primary" variant="default">
                        {contact.unread}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        <div className={`flex-1 flex flex-col ${!showMobileConversation ? 'hidden md:flex' : 'flex'}`}>
          {selectedContact ? (
            <>
              <div className="border-b p-3 md:p-4 flex items-center justify-between">
                <div className="flex items-center">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="md:hidden mr-2"
                    onClick={handleBackToContacts}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Avatar className="h-10 w-10 border">
                    <div className="flex h-full w-full items-center justify-center bg-primary/10 text-primary">
                      {getInitials(selectedContact.name)}
                    </div>
                  </Avatar>
                  <div className="ml-3">
                    <p className="font-medium">{selectedContact.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {selectedContact.status === "online" ? "Online" : `Last seen ${selectedContact.lastSeen}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Button variant="ghost" size="icon" className="hidden md:flex">
                    <Phone className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="icon" className="hidden md:flex">
                    <Video className="h-5 w-5" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-5 w-5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Contact</DropdownMenuItem>
                      <DropdownMenuItem>Search</DropdownMenuItem>
                      <DropdownMenuItem>Mute Notifications</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              <ScrollArea className="flex-1 p-3 md:p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sent ? "justify-end" : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[85%] md:max-w-[80%] rounded-lg p-3 ${
                          message.sent
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted"
                        }`}
                      >
                        <p className="break-words">{message.text}</p>
                        <p className={`text-xs mt-1 ${
                          message.sent ? "text-primary-foreground/70" : "text-muted-foreground"
                        }`}>
                          {formatMessageDate(message.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <div className="border-t p-3 md:p-4">
                <div className="flex items-center space-x-2">
                  {!isMobile && (
                    <>
                      <Button variant="ghost" size="icon">
                        <Plus className="h-5 w-5" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Image className="h-5 w-5" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Paperclip className="h-5 w-5" />
                      </Button>
                    </>
                  )}
                  <Input
                    className="flex-1"
                    placeholder="Type a message..."
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button 
                    size="icon" 
                    onClick={handleSendMessage}
                    disabled={messageText.trim() === ""}
                  >
                    <Send className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-6">
              <Users className="h-16 w-16 mb-4 text-muted-foreground" />
              <h3 className="text-xl font-medium mb-2">No conversation selected</h3>
              <p className="text-center text-muted-foreground">
                {isAdmin() 
                  ? "Select a staff member to start messaging" 
                  : "Select an administrator to start messaging"}
              </p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
