import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot,
  query,
  where,
  serverTimestamp,
  DocumentData
} from 'firebase/firestore';
import type { Product, FirestoreUser } from '@/types/firebase';

// Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyDvwbB4WUw_ehVzqFE1N0fhLlRfbfgUpjg",
  authDomain: "stationary-e71dc.firebaseapp.com",
  projectId: "stationary-e71dc",
  storageBucket: "stationary-e71dc.firebasestorage.app",
  messagingSenderId: "711863718942",
  appId: "1:711863718942:web:52df9e6dca61435ca9ca0f",
  measurementId: "G-FE32BTXR50"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const firebaseAuth = getAuth(app);
export const firebaseFirestore = getFirestore(app); // Added export

// Auth service
export const auth = {
  currentUser: firebaseAuth.currentUser,
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
      const userDoc = await getDoc(doc(firebaseFirestore, 'users', userCredential.user.uid));
      const userData = userDoc.exists() ? userDoc.data() as FirestoreUser : null;
      
      return { 
        user: { 
          uid: userCredential.user.uid, 
          email: userCredential.user.email, 
          displayName: userCredential.user.displayName || userData?.displayName || email.split('@')[0],
          role: userData?.role || 'shopkeeper'
        } 
      };
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  },
  async signOut() {
    try {
      await firebaseSignOut(firebaseAuth);
      return true;
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  },
  onAuthStateChanged,
};

// Firestore service
export const firestore = {
  collection: (name: string) => ({
    get: async () => {
      try {
        const querySnapshot = await getDocs(collection(firebaseFirestore, name));
        return {
          docs: querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })),
        };
      } catch (error) {
        console.error(`Error getting collection ${name}:`, error);
        throw error;
      }
    },
    add: async (data: DocumentData) => {
      try {
        const docRef = await addDoc(collection(firebaseFirestore, name), {
          ...data,
          createdAt: serverTimestamp(),
        });
        return { id: docRef.id };
      } catch (error) {
        console.error(`Error adding document to ${name}:`, error);
        throw error;
      }
    },
    doc: (id: string) => ({
      get: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          const docSnap = await getDoc(docRef);
          return {
            exists: docSnap.exists(),
            data: () => ({ id: docSnap.id, ...docSnap.data() }),
          };
        } catch (error) {
          console.error(`Error getting document ${name}/${id}:`, error);
          throw error;
        }
      },
      update: async (data: DocumentData) => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await updateDoc(docRef, data);
          return true;
        } catch (error) {
          console.error(`Error updating document ${name}/${id}:`, error);
          throw error;
        }
      },
      delete: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await deleteDoc(docRef);
          return true;
        } catch (error) {
          console.error(`Error deleting document ${name}/${id}:`, error);
          throw error;
        }
      },
    }),
    onSnapshot: (callback: (data: DocumentData[]) => void) => {
      const q = collection(firebaseFirestore, name);
      return onSnapshot(q, (querySnapshot) => {
        const items = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(items);
      });
    },
    where: (field: string, operator: any, value: any) => {
      const q = query(
        collection(firebaseFirestore, name),
        where(field, operator, value)
      );
      return {
        get: async () => {
          try {
            const querySnapshot = await getDocs(q);
            return {
              docs: querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })),
            };
          } catch (error) {
            console.error(`Error querying collection ${name}:`, error);
            throw error;
          }
        },
        onSnapshot: (callback: (data: DocumentData[]) => void) => {
          return onSnapshot(q, (querySnapshot) => {
            const items = querySnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(items);
          });
        }
      };
    }
  }),
};

// Create demo users in Firebase
export const createDemoData = async () => {
  try {
    // Check if we already have products
    const productsSnapshot = await getDocs(collection(firebaseFirestore, 'products'));
    if (productsSnapshot.empty) {
      const demoProducts = [
        { name: 'BIC Ballpoint Pen Blue', price: 1.25, stock: 50, category: 'Pens & Pencils', sku: 'PEN001', description: 'Classic blue ballpoint pen, smooth writing, reliable ink flow', threshold: 10 },
        { name: 'A4 Copy Paper 500 Sheets', price: 8.99, stock: 25, category: 'Paper Products', sku: 'PAP001', description: 'High-quality white A4 copy paper, 80gsm weight, perfect for printing and copying', threshold: 5 },
        { name: 'Stapler Heavy Duty', price: 15.99, stock: 8, category: 'Office Supplies', sku: 'OFF001', description: 'Heavy-duty stapler, staples up to 50 sheets, includes 1000 staples', threshold: 3 },
        { name: 'Colored Pencils Set 24pc', price: 12.50, stock: 15, category: 'Art Supplies', sku: 'ART001', description: 'Professional colored pencils set, vibrant colors, perfect for drawing and coloring', threshold: 5 },
        { name: 'Spiral Notebook A5', price: 3.75, stock: 30, category: 'Notebooks & Journals', sku: 'NOT001', description: 'A5 spiral-bound notebook, 200 pages, ruled lines, durable cover', threshold: 8 },
        { name: 'Highlighter Set 4 Colors', price: 6.99, stock: 20, category: 'Pens & Pencils', sku: 'PEN002', description: 'Fluorescent highlighter set in yellow, pink, green, and blue', threshold: 6 },
        { name: 'File Folders Letter Size', price: 4.25, stock: 12, category: 'Filing & Organization', sku: 'FIL001', description: 'Manila file folders, letter size, pack of 25, perfect for document organization', threshold: 4 },
        { name: 'Desk Organizer Bamboo', price: 22.99, stock: 6, category: 'Desk Accessories', sku: 'DSK001', description: 'Eco-friendly bamboo desk organizer with multiple compartments for pens, clips, and supplies', threshold: 2 },
        { name: 'Sticky Notes 3x3 Yellow', price: 2.99, stock: 40, category: 'Stickers & Labels', sku: 'STK001', description: 'Classic yellow sticky notes, 3x3 inches, pack of 100 sheets, strong adhesive', threshold: 10 },
        { name: 'Correction Tape', price: 3.50, stock: 0, category: 'Office Supplies', sku: 'OFF002', description: 'White correction tape, 5mm width, easy application, clean coverage', threshold: 8 },
      ];
      
      for (const product of demoProducts) {
        await addDoc(collection(firebaseFirestore, 'products'), {
          ...product,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo products created');
    }

    // Check if we already have users
    const usersSnapshot = await getDocs(collection(firebaseFirestore, 'users'));
    if (usersSnapshot.empty) {
      const demoUsers = [
        { email: '<EMAIL>', displayName: 'Store Manager', role: 'admin' },
        { email: '<EMAIL>', displayName: 'Sales Associate', role: 'shopkeeper' },
      ];
      
      for (const user of demoUsers) {
        await addDoc(collection(firebaseFirestore, 'users'), {
          ...user,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo user documents created');
    }
  } catch (error) {
    console.error('Error creating demo data:', error);
  }
};

// Initialize demo data
createDemoData();

export default { auth, firestore, createDemoData };