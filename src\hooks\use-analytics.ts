
import { useState, useEffect } from 'react';
import { firebaseFirestore } from '@/lib/firebase';
import { collection, query, getDocs, where, orderBy, limit } from 'firebase/firestore';

type SalesData = {
  amount: number;
  date: string;
  product: {
    name: string;
    price: number;
  };
};

export const useRecentSales = () => {
  const [sales, setSales] = useState<SalesData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecentSales = async () => {
      try {
        setIsLoading(true);
        const salesCollection = collection(firebaseFirestore, 'sales');
        const salesQuery = query(salesCollection, orderBy('date', 'desc'), limit(5));
        const salesSnapshot = await getDocs(salesQuery);
        
        const salesData = salesSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            amount: data.amount || 0,
            date: data.date || new Date().toISOString(),
            product: data.product || { name: 'Unknown Product', price: 0 }
          } as SalesData;
        });
        
        setSales(salesData);
      } catch (err) {
        console.error('Error fetching recent sales:', err);
        setError('Failed to load recent sales');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentSales();
  }, []);

  return { sales, isLoading, error };
};

export const useTopProducts = () => {
  const [products, setProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopProducts = async () => {
      try {
        setIsLoading(true);
        // In a real app, we would query for top products based on sales
        // For now, we'll just get a few products and pretend they're top sellers
        const productsCollection = collection(firebaseFirestore, 'products');
        const productsQuery = query(productsCollection, limit(5));
        const productsSnapshot = await getDocs(productsQuery);
        
        const productsData = productsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          sales: Math.floor(Math.random() * 50) + 5 // Fake sales data
        }));
        
        setProducts(productsData);
      } catch (err) {
        console.error('Error fetching top products:', err);
        setError('Failed to load top products');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopProducts();
  }, []);

  return { products, isLoading, error };
};

export const useSalesByCategory = () => {
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSalesByCategory = async () => {
      try {
        setIsLoading(true);
        // In a real app, we would aggregate sales by category
        // For now, we'll create mock data
        const categoriesData = [
          { name: 'Electronics', value: 35 },
          { name: 'Clothing', value: 25 },
          { name: 'Food', value: 20 },
          { name: 'Books', value: 15 },
          { name: 'Other', value: 5 }
        ];
        
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error fetching sales by category:', err);
        setError('Failed to load category data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSalesByCategory();
  }, []);

  return { categories, isLoading, error };
};
