
import { useState } from "react";
import { 
  Calendar, 
  Download, 
  <PERSON><PERSON>ex<PERSON>, 
  Filter, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Up,
  DollarSign,
  Package
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Bar<PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from "recharts";

// Mock data for sales report
const monthlySalesData = [
  { month: "Jan", sales: 25000, target: 22000 },
  { month: "Feb", sales: 28000, target: 25000 },
  { month: "Mar", sales: 32000, target: 28000 },
  { month: "Apr", sales: 30000, target: 30000 },
  { month: "May", sales: 35000, target: 32000 },
  { month: "Jun", sales: 37000, target: 35000 },
  { month: "Jul", sales: 33000, target: 37000 },
  { month: "Aug", sales: 36000, target: 38000 },
  { month: "Sep", sales: 39000, target: 40000 },
  { month: "Oct", sales: 41000, target: 42000 },
  { month: "Nov", sales: 45000, target: 44000 },
  { month: "Dec", sales: 50000, target: 46000 },
];

// Mock data for category breakdown
const categoryData = [
  { name: "Electronics", value: 45 },
  { name: "Clothing", value: 20 },
  { name: "Home Goods", value: 15 },
  { name: "Sports", value: 10 },
  { name: "Books", value: 10 },
];

// Mock data for inventory turnover
const inventoryTurnoverData = [
  { category: "Electronics", turnover: 7.2 },
  { category: "Clothing", turnover: 5.8 },
  { category: "Home Goods", turnover: 4.5 },
  { category: "Sports", turnover: 3.9 },
  { category: "Books", turnover: 2.7 },
];

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Report types
const reportTypes = [
  "Sales Report",
  "Inventory Report",
  "Financial Report",
  "Customer Report",
  "Employee Report"
];

// Time periods
const timePeriods = [
  "Last 7 Days",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "This Quarter",
  "Last Quarter",
  "This Year",
  "Last Year",
  "Custom Range"
];

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState("Sales Report");
  const [selectedPeriod, setSelectedPeriod] = useState("This Month");

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
        <p className="text-muted-foreground">
          Generate and view detailed reports about your business performance.
        </p>
      </div>

      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="flex flex-col md:flex-row gap-2">
          <Select value={selectedReport} onValueChange={setSelectedReport}>
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              {reportTypes.map((type) => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent>
              {timePeriods.map((period) => (
                <SelectItem key={period} value={period}>{period}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Custom Date
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
          <Button size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="charts" className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto">
          <TabsTrigger value="charts">Charts</TabsTrigger>
          <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
        </TabsList>
        
        <TabsContent value="charts">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Sales vs Targets</CardTitle>
                <CardDescription>Performance against monthly targets</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={monthlySalesData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                          border: 'none',
                        }}
                        formatter={(value) => [`$${value}`, '']}
                      />
                      <Legend />
                      <Bar dataKey="sales" name="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="target" name="Target" fill="hsl(var(--muted))" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Sales growth over the year</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={monthlySalesData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                          border: 'none',
                        }}
                        formatter={(value) => [`$${value}`, '']}
                      />
                      <Legend />
                      <Line type="monotone" dataKey="sales" name="Sales" stroke="hsl(var(--primary))" strokeWidth={2} dot={{ r: 4 }} />
                      <Line type="monotone" dataKey="target" name="Target" stroke="hsl(var(--muted))" strokeWidth={2} dot={{ r: 4 }} strokeDasharray="5 5" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Sales by Category</CardTitle>
                <CardDescription>Percentage breakdown by product category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RePieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`${value}%`, 'Percentage']}
                        contentStyle={{
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                          border: 'none',
                        }}
                      />
                    </RePieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Inventory Turnover</CardTitle>
                <CardDescription>By product category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={inventoryTurnoverData}
                      layout="vertical"
                      margin={{ top: 20, right: 30, left: 60, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                      <XAxis type="number" />
                      <YAxis dataKey="category" type="category" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                          border: 'none',
                        }}
                        formatter={(value) => [`${value} times`, 'Turnover Rate']}
                      />
                      <Legend />
                      <Bar dataKey="turnover" name="Turnover Rate" fill="#8884d8" radius={[0, 4, 4, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="metrics">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$394,820.00</div>
                <p className="text-xs text-muted-foreground">
                  +12.5% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">32.8%</div>
                <p className="text-xs text-muted-foreground">
                  +2.1% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$128.50</div>
                <p className="text-xs text-muted-foreground">
                  +5.4% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Units Sold</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3,241</div>
                <p className="text-xs text-muted-foreground">
                  +8.2% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Return Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.4%</div>
                <p className="text-xs text-muted-foreground text-green-500">
                  -0.8% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Customer Acquisition Cost</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$24.80</div>
                <p className="text-xs text-muted-foreground text-green-500">
                  -3.2% from previous period
                </p>
              </CardContent>
              <CardFooter className="border-t pt-2">
                <Button variant="ghost" size="sm" className="w-full justify-start px-2">
                  <FileText className="mr-2 h-4 w-4" />
                  View Full Report
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="breakdown">
          <div className="grid gap-6 md:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Available Reports</CardTitle>
                <CardDescription>Select a report to download or view details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { 
                      title: "Monthly Sales Report", 
                      description: "Detailed sales figures for the month with comparisons to previous periods", 
                      icon: <BarChart3 className="h-8 w-8 text-primary" />,
                      date: "Generated: May 1, 2023"
                    },
                    { 
                      title: "Quarterly Financial Statement", 
                      description: "Complete P&L statement, balance sheet, and cash flow analysis", 
                      icon: <DollarSign className="h-8 w-8 text-emerald-500" />,
                      date: "Generated: Apr 15, 2023"
                    },
                    { 
                      title: "Inventory Status Report", 
                      description: "Current stock levels, reorder suggestions, and inventory valuation", 
                      icon: <Package className="h-8 w-8 text-blue-500" />,
                      date: "Generated: Apr 28, 2023"
                    },
                    { 
                      title: "Annual Performance Review", 
                      description: "Yearly business performance metrics and growth analysis", 
                      icon: <TrendingUp className="h-8 w-8 text-amber-500" />,
                      date: "Generated: Mar 31, 2023"
                    },
                    { 
                      title: "Sales by Category Analysis", 
                      description: "Breakdown of sales performance by product categories", 
                      icon: <PieChart className="h-8 w-8 text-purple-500" />,
                      date: "Generated: Apr 22, 2023"
                    },
                  ].map((report, index) => (
                    <div key={index} className="flex items-start space-x-4 rounded-lg border p-4 hover:bg-muted/50 transition-colors">
                      <div className="rounded-md bg-background p-2">
                        {report.icon}
                      </div>
                      <div className="flex-1 space-y-1">
                        <h3 className="font-medium">{report.title}</h3>
                        <p className="text-sm text-muted-foreground">{report.description}</p>
                        <p className="text-xs text-muted-foreground">{report.date}</p>
                      </div>
                      <div>
                        <Button variant="outline" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate New Report
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
