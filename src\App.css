
#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: left;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 1em;
}

@media (min-width: 768px) {
  .card {
    padding: 2em;
  }
}

.read-the-docs {
  color: #888;
}

/* Custom responsive styles */

@media (max-width: 640px) {
  h1 {
    font-size: 1.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
  
  .compact-padding {
    padding: 0.5rem !important;
  }
  
  .hide-on-mobile {
    display: none !important;
  }
}

/* Responsive table styles */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .table-compact th,
  .table-compact td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

/* Better touch targets for mobile */
@media (max-width: 640px) {
  button, 
  .button, 
  [role="button"],
  a {
    min-height: 44px;
    min-width: 44px;
  }
}
