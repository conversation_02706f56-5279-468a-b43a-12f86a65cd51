
import { useState } from "react";
import { 
  Search, 
  Plus,
  Download,
  Upload,
  Package,
  Filter
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { AddProductForm } from "@/components/inventory/AddProductForm";
import { useQuery } from "@tanstack/react-query";
import { firestore } from "@/lib/firebase";
import type { Product } from '@/types/firebase';
import { getFirestore, collection, getDocs } from "firebase/firestore";

export default function Inventory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [sortOrder, setSortOrder] = useState("name-asc");
  const [showAddForm, setShowAddForm] = useState(false);
  const { toast } = useToast();

  const { data: productsData = [], isLoading, refetch } = useQuery<Product[]>({
    queryKey: ["products"],
    queryFn: async () => {
      // Direct Firestore access for more reliable fetching
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const querySnapshot = await getDocs(productsCollection);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Product, 'id'>)
      }));
    }
  });

  const inventoryData = productsData;

  const filteredData = inventoryData.filter((item: any) => {
    const matchesSearch = item.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         item.sku?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "All Categories" || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const sortedData = [...filteredData].sort((a: any, b: any) => {
    const [field, direction] = sortOrder.split("-");
    
    if (field === "name") {
      return direction === "asc" 
        ? a.name?.localeCompare(b.name) 
        : b.name?.localeCompare(a.name);
    } else if (field === "price") {
      return direction === "asc" 
        ? a.price - b.price 
        : b.price - a.price;
    } else if (field === "stock") {
      return direction === "asc" 
        ? a.stock - b.stock 
        : b.stock - a.stock;
    }
    return 0;
  });

  const outOfStock = inventoryData.filter((item: any) => item.stock === 0).length;
  const lowStock = inventoryData.filter((item: any) => item.stock > 0 && item.stock <= 5).length;
  const inStock = inventoryData.filter((item: any) => item.stock > 5).length;

  const categoriesSet = new Set(["All Categories"]);
  inventoryData.forEach((item: any) => {
    if (item.category) categoriesSet.add(item.category);
  });
  const categories = Array.from(categoriesSet);

  const handleExportCSV = () => {
    const headers = ["Name", "Category", "SKU", "Price", "Stock"];
    const csvRows = [headers.join(",")];
    
    for (const item of sortedData) {
      const values = [
        `"${item.name || ''}"`,
        `"${item.category || ''}"`,
        `"${item.sku || ''}"`,
        `${item.price || 0}`,
        `${item.stock || 0}`
      ];
      csvRows.push(values.join(","));
    }
    
    const csvString = csvRows.join("\n");
    
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    
    link.setAttribute("href", url);
    link.setAttribute("download", "inventory.csv");
    link.style.visibility = "hidden";
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Export Successful",
      description: `${sortedData.length} products exported to CSV`
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Stationery Inventory</h1>
        <p className="text-muted-foreground">
          Manage your stationery products, monitor stock levels, and track office supplies inventory.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              In Stock
            </CardTitle>
            <div className="h-4 w-4 text-green-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inStock}</div>
            <p className="text-xs text-muted-foreground">
              Stationery items with good stock levels
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Low Stock
            </CardTitle>
            <div className="h-4 w-4 text-amber-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{lowStock}</div>
            <p className="text-xs text-muted-foreground">
              Stationery items that need restocking
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Out of Stock
            </CardTitle>
            <div className="h-4 w-4 text-red-500">
              <Package size={16} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{outOfStock}</div>
            <p className="text-xs text-muted-foreground">
              Items completely out of stock
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search stationery items..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                <SelectItem value="price-asc">Price (Low-High)</SelectItem>
                <SelectItem value="price-desc">Price (High-Low)</SelectItem>
                <SelectItem value="stock-asc">Stock (Low-High)</SelectItem>
                <SelectItem value="stock-desc">Stock (High-Low)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2 md:flex-row">
            <Button
              variant="outline"
              size="sm"
              className="md:w-auto"
              onClick={handleExportCSV}
            >
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
              <DialogTrigger asChild>
                <Button size="sm" className="md:w-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[90vw] md:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <AddProductForm
                  onClose={() => setShowAddForm(false)}
                  onProductAdded={() => {
                    refetch();
                  }}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead className="text-right">Price</TableHead>
                <TableHead className="text-right">Stock</TableHead>
                <TableHead className="text-right">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    <div className="flex justify-center">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : sortedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No products found
                  </TableCell>
                </TableRow>
              ) : (
                sortedData.map((product: any) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell className="font-mono text-xs">{product.sku}</TableCell>
                    <TableCell className="text-right">${product.price?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell className="text-right">{product.stock}</TableCell>
                    <TableCell className="text-right">
                      {product.stock === 0 ? (
                        <Badge variant="destructive">Out of Stock</Badge>
                      ) : product.stock <= 5 ? (
                        <Badge variant="outline" className="bg-amber-100 text-amber-700 hover:bg-amber-100 border-amber-200">
                          Low Stock
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-green-100 text-green-700 hover:bg-green-100 border-green-200">
                          In Stock
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
