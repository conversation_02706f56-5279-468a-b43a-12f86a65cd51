
import { useState } from "react";
import { 
  Search, 
  Plus, 
  Filter,
  Download,
  Upload,
  Mail,
  MoreHorizontal,
  UserPlus,
  Trash2,
  Edit,
  Award
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MetricCard } from "@/components/common/MetricCard";
import { useAuth } from "@/context/AuthContext";

// Mock data for staff performance
const staffData = [
  { id: "1", name: "<PERSON>", position: "Store Manager", email: "<EMAIL>", phone: "555-1234", performance: 95, status: "Active" },
  { id: "2", name: "Jane <PERSON>", position: "Assistant Manager", email: "<EMAIL>", phone: "555-2345", performance: 88, status: "Active" },
  { id: "3", name: "Robert Johnson", position: "Sales Associate", email: "<EMAIL>", phone: "555-3456", performance: 78, status: "Active" },
  { id: "4", name: "Emily Davis", position: "Cashier", email: "<EMAIL>", phone: "555-4567", performance: 92, status: "Active" },
  { id: "5", name: "Michael Wilson", position: "Inventory Specialist", email: "<EMAIL>", phone: "555-5678", performance: 85, status: "Active" },
  { id: "6", name: "Sarah Brown", position: "Sales Associate", email: "<EMAIL>", phone: "555-6789", performance: 79, status: "Active" },
  { id: "7", name: "David Miller", position: "Cashier", email: "<EMAIL>", phone: "555-7890", performance: 82, status: "On Leave" },
  { id: "8", name: "Jessica Garcia", position: "Sales Associate", email: "<EMAIL>", phone: "555-8901", performance: 91, status: "Active" },
  { id: "9", name: "Thomas Rodriguez", position: "Inventory Specialist", email: "<EMAIL>", phone: "555-9012", performance: 75, status: "Active" },
  { id: "10", name: "Jennifer Martinez", position: "Cashier", email: "<EMAIL>", phone: "555-0123", performance: 87, status: "Inactive" },
];

// Positions for dropdown filter
const positions = [
  "All Positions",
  "Store Manager",
  "Assistant Manager",
  "Sales Associate",
  "Cashier",
  "Inventory Specialist",
];

export default function StaffPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPosition, setSelectedPosition] = useState("All Positions");
  const [selectedStatus, setSelectedStatus] = useState("All Status");
  const { isAdmin } = useAuth();

  // Filter staff data based on search term and filters
  const filteredData = staffData.filter(staff => {
    const matchesSearch = 
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.position.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesPosition = selectedPosition === "All Positions" || staff.position === selectedPosition;
    const matchesStatus = selectedStatus === "All Status" || staff.status === selectedStatus;
    
    return matchesSearch && matchesPosition && matchesStatus;
  });

  // Calculate staff statistics
  const activeStaff = staffData.filter(staff => staff.status === "Active").length;
  const onLeaveStaff = staffData.filter(staff => staff.status === "On Leave").length;
  const inactiveStaff = staffData.filter(staff => staff.status === "Inactive").length;
  const averagePerformance = Math.round(
    staffData.reduce((acc, staff) => acc + staff.performance, 0) / staffData.length
  );

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Staff Management</h1>
        <p className="text-muted-foreground">
          Manage your team members and monitor their performance.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Active Staff"
          value={activeStaff.toString()}
          description="Currently working"
          icon={<UserPlus className="h-4 w-4" />}
        />
        <MetricCard
          title="Average Performance"
          value={`${averagePerformance}%`}
          description="Across all staff"
          icon={<Award className="h-4 w-4" />}
          trend={{ value: 3, positive: true }}
        />
        <MetricCard
          title="On Leave"
          value={onLeaveStaff.toString()}
          description="Temporarily away"
          icon={<Mail className="h-4 w-4" />}
        />
        <MetricCard
          title="Inactive"
          value={inactiveStaff.toString()}
          description="No longer employed"
          icon={<Trash2 className="h-4 w-4" />}
        />
      </div>

      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search staff..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={selectedPosition} onValueChange={setSelectedPosition}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Position" />
              </SelectTrigger>
              <SelectContent>
                {positions.map((position) => (
                  <SelectItem key={position} value={position}>{position}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Status">All Status</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="On Leave">On Leave</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {isAdmin() && (
            <div className="flex flex-col gap-2 md:flex-row">
              <Button variant="outline" size="sm" className="md:w-auto">
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
              <Button variant="outline" size="sm" className="md:w-auto">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button size="sm" className="md:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                Add Staff
              </Button>
            </div>
          )}
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Staff Directory</CardTitle>
            <CardDescription>Manage and monitor your team members</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Status</TableHead>
                  {isAdmin() && <TableHead className="text-right">Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={isAdmin() ? 6 : 5} className="h-24 text-center">
                      No staff members found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredData.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">{staff.name}</TableCell>
                      <TableCell>{staff.position}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm">{staff.email}</span>
                          <span className="text-xs text-muted-foreground">{staff.phone}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div 
                              className={`h-2.5 rounded-full ${getPerformanceColor(staff.performance)}`} 
                              style={{ width: `${staff.performance}%` }}
                            ></div>
                          </div>
                          <span className="text-sm">{staff.performance}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant="outline" 
                          className={getStatusBadgeClass(staff.status)}
                        >
                          {staff.status}
                        </Badge>
                      </TableCell>
                      {isAdmin() && (
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail className="mr-2 h-4 w-4" />
                                Message
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Helper functions for styling
function getPerformanceColor(performance: number) {
  if (performance >= 90) return "bg-green-500";
  if (performance >= 75) return "bg-blue-500";
  if (performance >= 60) return "bg-yellow-500";
  return "bg-red-500";
}

function getStatusBadgeClass(status: string) {
  switch (status) {
    case "Active":
      return "bg-green-100 text-green-700 hover:bg-green-100 border-green-200";
    case "On Leave":
      return "bg-amber-100 text-amber-700 hover:bg-amber-100 border-amber-200";
    case "Inactive":
      return "bg-red-100 text-red-700 hover:bg-red-100 border-red-200";
    default:
      return "bg-gray-100 text-gray-700 hover:bg-gray-100 border-gray-200";
  }
}
