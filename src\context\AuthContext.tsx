
import React, { createContext, useContext, useState, useEffect } from 'react';
import { auth as firebaseAuth } from '../lib/firebase';
import { getAuth, User as FirebaseUser, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import type { FirestoreUser } from '@/types/firebase';

type User = FirestoreUser | null;

type AuthContextType = {
  currentUser: User;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isAdmin: () => boolean;
  isShopkeeper: () => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const auth = getAuth();
    const db = getFirestore();

    const unsubscribe = onAuthStateChanged(auth, async (user: FirebaseUser | null) => {
      if (user) {
        try {
          // First try to get user document by uid
          const userDoc = await getDoc(doc(db, 'users', user.uid));
          
          if (userDoc.exists()) {
            const userData = userDoc.data() as Omit<FirestoreUser, 'uid'>;
            setCurrentUser({
              uid: user.uid,
              email: user.email,
              displayName: userData.displayName || user.displayName,
              role: userData.role as "admin" | "shopkeeper" || 'shopkeeper',
            });
          } else {
            // If no document with uid, try finding by email
            const usersRef = collection(db, 'users');
            const q = query(usersRef, where('email', '==', user.email));
            const querySnapshot = await getDocs(q);
            
            if (!querySnapshot.empty) {
              const userData = querySnapshot.docs[0].data() as Omit<FirestoreUser, 'uid'>;
              setCurrentUser({
                uid: user.uid,
                email: user.email,
                displayName: userData.displayName || user.displayName,
                role: userData.role as "admin" | "shopkeeper" || 'shopkeeper',
              });
            } else {
              // If no user document exists anywhere, create basic user data
              setCurrentUser({
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                role: 'shopkeeper',
              });
            }
          }
        } catch (err) {
          console.error('Error fetching user data:', err);
          setError('Failed to load user data');
        }
      } else {
        setCurrentUser(null);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await firebaseAuth.signIn(email, password);
      
      // Fetch additional user data from Firestore
      const db = getFirestore();
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);
      
      let role: "admin" | "shopkeeper" = 'shopkeeper';
      
      if (!querySnapshot.empty) {
        const userData = querySnapshot.docs[0].data();
        role = (userData.role as "admin" | "shopkeeper") || 'shopkeeper';
      }
      
      // Set user with role from Firestore
      setCurrentUser({
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName || email.split('@')[0],
        role
      });
      
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message || 'Failed to log in');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await firebaseAuth.signOut();
      setCurrentUser(null);
    } catch (err: any) {
      setError(err.message || 'Failed to log out');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const isAdmin = () => currentUser?.role === 'admin';
  const isShopkeeper = () => currentUser?.role === 'shopkeeper';

  const value: AuthContextType = {
    currentUser,
    isLoading,
    error,
    login,
    logout,
    isAdmin,
    isShopkeeper,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
