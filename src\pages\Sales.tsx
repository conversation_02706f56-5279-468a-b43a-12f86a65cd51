
import { useState } from "react";
import { 
  Card, 
  Card<PERSON>ontent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/common/DataTable";
import { MetricCard } from "@/components/common/MetricCard";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { firestore } from "@/lib/firebase";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { RecordSaleForm } from "@/components/sales/RecordSaleForm";
import {
  Bar<PERSON>hart,
  LineChart,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  ShoppingCart,
  Users,
  Calendar,
  Plus
} from "lucide-react";
import { BarChart as RechartsBarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, Cartesian<PERSON>rid, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart as <PERSON><PERSON>rts<PERSON>ine<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import type { Sale } from '@/types/firebase';
import { getFirestore, collection, getDocs } from "firebase/firestore";

export default function Sales() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showSaleForm, setShowSaleForm] = useState(false);
  
  const { data: salesData = [], isLoading: isLoadingSales, refetch: refetchSales } = useQuery<Sale[]>({
    queryKey: ["sales"],
    queryFn: async () => {
      // Direct Firestore access for more reliable fetching
      const db = getFirestore();
      const salesCollection = collection(db, "sales");
      const querySnapshot = await getDocs(salesCollection);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Sale, 'id'>)
      }));
    }
  });

  const salesColumns = [
    {
      id: "date",
      header: "Date",
      cell: (sale: any) => (
        <div className="flex items-center">
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{sale.date}</span>
        </div>
      ),
    },
    {
      id: "customer",
      header: "Customer",
      cell: (sale: any) => (
        <div className="flex items-center">
          <Users className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{sale.customer}</span>
        </div>
      ),
    },
    {
      id: "items",
      header: "Items",
      cell: (sale: any) => (
        <div className="flex items-center">
          <ShoppingCart className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{sale.items}</span>
        </div>
      ),
    },
    {
      id: "amount",
      header: "Amount",
      cell: (sale: any) => (
        <div className="flex items-center font-medium">
          <DollarSign className="mr-1 h-4 w-4 text-green-500" />
          <span>${sale.amount?.toFixed(2) || '0.00'}</span>
        </div>
      ),
    },
    {
      id: "status",
      header: "Status",
      cell: (sale: any) => {
        const statusMap: Record<string, { label: string, variant: "default" | "secondary" | "outline" | "destructive" }> = {
          completed: { label: "Completed", variant: "default" },
          pending: { label: "Pending", variant: "secondary" },
          canceled: { label: "Canceled", variant: "destructive" },
        };
        
        const status = statusMap[sale.status || "completed"];
        
        return (
          <Badge variant={status.variant}>
            {status.label}
          </Badge>
        );
      },
    },
  ];

  const totalSales = salesData?.reduce((sum: number, sale: any) => sum + (sale.amount || 0), 0) || 0;
  const averageTransaction = salesData?.length ? totalSales / salesData.length : 0;
  const totalTransactions = salesData?.length || 0;
  
  const getThisWeekSales = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return salesData?.reduce((sum: number, sale: any) => {
      const saleDate = new Date(sale.date || sale.timestamp);
      return saleDate >= startOfWeek ? sum + (sale.amount || 0) : sum;
    }, 0) || 0;
  };

  const thisWeekSales = getThisWeekSales();

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Stationery Sales</h1>
          <p className="text-muted-foreground">
            Track and manage your stationery sales and customer transactions.
          </p>
        </div>
        <Dialog open={showSaleForm} onOpenChange={setShowSaleForm}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Record Sale
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[90vw] md:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <RecordSaleForm
              onClose={() => setShowSaleForm(false)}
              onSaleRecorded={() => {
                refetchSales();
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Total Sales"
              value={`$${totalSales.toFixed(2)}`}
              description="All time sales value"
              icon={<DollarSign />}
              trend={{ value: 12.5, positive: true }}
            />
            <MetricCard
              title="Sales This Week"
              value={`$${thisWeekSales.toFixed(2)}`}
              description="This week's performance"
              icon={<BarChart />}
              trend={{ value: 4.2, positive: true }}
            />
            <MetricCard
              title="Avg. Transaction"
              value={`$${averageTransaction.toFixed(2)}`}
              description="Per sale average"
              icon={<ArrowUpRight />}
            />
            <MetricCard
              title="Total Transactions"
              value={totalTransactions}
              description="Number of sales"
              icon={<ShoppingCart />}
              trend={{ value: 2.1, positive: false }}
            />
          </div>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Sales</CardTitle>
                <CardDescription>View your most recent sales transactions</CardDescription>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowSaleForm(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                New Sale
              </Button>
            </CardHeader>
            <CardContent>
              <DataTable
                data={salesData || []}
                columns={salesColumns}
                isLoading={isLoadingSales}
                emptyMessage="No sales records found"
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>All Transactions</CardTitle>
                <CardDescription>Complete list of all sales transactions</CardDescription>
              </div>
              <Button 
                onClick={() => setShowSaleForm(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Record Sale
              </Button>
            </CardHeader>
            <CardContent>
              <DataTable
                data={salesData || []}
                columns={salesColumns}
                isLoading={isLoadingSales}
                emptyMessage="No transactions available"
              />
            </CardContent>
          </Card>
        </TabsContent>
        

      </Tabs>
    </div>
  );
}
