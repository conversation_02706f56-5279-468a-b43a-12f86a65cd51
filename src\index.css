
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 48%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 221 83% 53%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 224 76% 48%;

    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Improved typography for mobile */
  h1 {
    @apply text-2xl md:text-3xl font-bold;
  }
  
  h2 {
    @apply text-xl md:text-2xl font-semibold;
  }
  
  h3 {
    @apply text-lg md:text-xl font-medium;
  }
  
  p {
    @apply text-sm md:text-base;
  }
}

@layer components {
  .glass-card {
    @apply bg-white bg-opacity-70 backdrop-blur-glass shadow-glass 
           border border-white border-opacity-20 
           transition-all duration-300 ease-in-out;
  }
  
  .glass-card:hover {
    @apply shadow-glass-hover;
  }
  
  .fade-in {
    @apply animate-fade-in;
  }
  
  .scale-in {
    @apply animate-scale-in;
  }
  
  .slide-in {
    @apply animate-slide-in;
  }
  
  /* Responsive component classes */
  .responsive-container {
    @apply px-3 sm:px-4 md:px-6 py-4 md:py-6;
  }
  
  .responsive-grid {
    @apply grid gap-3 sm:gap-4 md:gap-6;
  }
  
  .responsive-card {
    @apply p-3 sm:p-4 md:p-6;
  }
  
  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
}

/* Media query for small screens */
@media (max-width: 640px) {
  .hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .compact-text {
    font-size: 0.9em;
  }
  
  .compact-padding {
    padding: 0.5rem !important;
  }
}
