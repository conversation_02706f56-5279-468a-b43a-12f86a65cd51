
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Download, FileText, FileCog } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getFirestore, collection, getDocs, query, where, orderBy, Timestamp } from "firebase/firestore";

export default function Reports() {
  const [activeTab, setActiveTab] = useState("sales");
  const [reportType, setReportType] = useState("daily");
  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });
  const [category, setCategory] = useState("all");
  const { toast } = useToast();

  // Format date for display
  const formatDate = (date: Date) => {
    return format(date, "PPP");
  };

  // Query for sales data
  const { data: salesData = [], isLoading: isLoadingSales } = useQuery({
    queryKey: ["sales", reportType, dateRange, category],
    queryFn: async () => {
      const db = getFirestore();
      const salesCollection = collection(db, "sales");
      
      let salesQuery = query(salesCollection, orderBy("date", "desc"));
      
      // Add date range filter
      if (dateRange.from && dateRange.to) {
        const fromDate = Timestamp.fromDate(dateRange.from);
        const toDate = Timestamp.fromDate(dateRange.to);
        salesQuery = query(salesQuery, where("date", ">=", fromDate), where("date", "<=", toDate));
      }
      
      // Add category filter
      if (category !== "all") {
        salesQuery = query(salesQuery, where("product.category", "==", category));
      }
      
      const snapshot = await getDocs(salesQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    }
  });

  // Query for inventory data
  const { data: inventoryData = [], isLoading: isLoadingInventory } = useQuery({
    queryKey: ["inventory", category],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      
      let productsQuery = query(productsCollection, orderBy("name"));
      
      // Add category filter
      if (category !== "all") {
        productsQuery = query(productsQuery, where("category", "==", category));
      }
      
      const snapshot = await getDocs(productsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    }
  });

  // Query for product categories
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ["categories"],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const snapshot = await getDocs(productsCollection);
      
      // Extract unique categories
      const uniqueCategories = new Set<string>();
      snapshot.docs.forEach(doc => {
        const category = doc.data().category;
        if (category) uniqueCategories.add(category);
      });
      
      return Array.from(uniqueCategories);
    }
  });

  // Calculate totals
  const calculateTotals = () => {
    if (!salesData?.length) return { totalSales: 0, totalItems: 0, averageSale: 0 };
    
    const totalSales = salesData.reduce((sum: number, sale: any) => sum + (sale.amount || 0), 0);
    const totalItems = salesData.length;
    const averageSale = totalItems > 0 ? totalSales / totalItems : 0;
    
    return { totalSales, totalItems, averageSale };
  };
  
  const { totalSales, totalItems, averageSale } = calculateTotals();

  // Handle generating report
  const handleGenerateReport = () => {
    toast({
      title: "Report Generated",
      description: `Your ${reportType} report has been generated.`,
    });
  };

  // Handle download report
  const handleDownloadReport = () => {
    toast({
      title: "Report Downloaded",
      description: "Your report has been downloaded.",
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        <p className="text-muted-foreground">
          Generate and view detailed reports about your shop's performance.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="sales">
            <FileText className="mr-2 h-4 w-4" />
            Sales Reports
          </TabsTrigger>
          <TabsTrigger value="inventory">
            <FileCog className="mr-2 h-4 w-4" />
            Inventory Reports
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sales Report Parameters</CardTitle>
              <CardDescription>
                Configure the parameters for your sales report.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="report-type">Report Type</Label>
                  <Select
                    value={reportType}
                    onValueChange={setReportType}
                  >
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily Report</SelectItem>
                      <SelectItem value="weekly">Weekly Report</SelectItem>
                      <SelectItem value="monthly">Monthly Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <div className="grid gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange?.from ? (
                            dateRange.to ? (
                              <>
                                {formatDate(dateRange.from)} - {formatDate(dateRange.to)}
                              </>
                            ) : (
                              formatDate(dateRange.from)
                            )
                          ) : (
                            <span>Pick a date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          initialFocus
                          mode="range"
                          defaultMonth={dateRange?.from}
                          selected={dateRange}
                          onSelect={(range) => setDateRange(range || { from: new Date(), to: new Date() })}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Product Category</Label>
                  <Select
                    value={category}
                    onValueChange={setCategory}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {isLoadingCategories ? (
                        <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                      ) : (
                        categories.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-2 justify-end">
                <Button variant="outline" onClick={handleGenerateReport}>
                  Generate Report
                </Button>
                <Button onClick={handleDownloadReport}>
                  <Download className="mr-2 h-4 w-4" />
                  Download Report
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Sales
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {totalSales.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  For the selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Items Sold
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalItems}</div>
                <p className="text-xs text-muted-foreground">
                  Total number of sales
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Average Sale
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Tsh {averageSale.toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
                <p className="text-xs text-muted-foreground">
                  Average sale amount
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Sales Transactions</CardTitle>
              <CardDescription>
                Detailed list of sales transactions for the selected period.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingSales ? (
                <div className="flex justify-center p-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : salesData.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No sales data available for the selected criteria.
                </div>
              ) : (
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Product</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Quantity</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {salesData.map((sale: any) => (
                        <tr key={sale.id} className="border-b">
                          <td className="p-4 align-middle">
                            {sale.date && typeof sale.date.toDate === 'function' 
                              ? format(sale.date.toDate(), "MMM dd, yyyy") 
                              : "Unknown date"}
                          </td>
                          <td className="p-4 align-middle">{sale.product?.name || "Unknown product"}</td>
                          <td className="p-4 align-middle">{sale.quantity || 1}</td>
                          <td className="p-4 align-middle">Tsh {sale.amount?.toLocaleString() || "0"}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Report Parameters</CardTitle>
              <CardDescription>
                Configure the parameters for your inventory report.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="inv-category">Product Category</Label>
                  <Select
                    value={category}
                    onValueChange={setCategory}
                  >
                    <SelectTrigger id="inv-category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {isLoadingCategories ? (
                        <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                      ) : (
                        categories.map((category: string) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stock-status">Stock Status</Label>
                  <Select defaultValue="all">
                    <SelectTrigger id="stock-status">
                      <SelectValue placeholder="Select stock status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stock</SelectItem>
                      <SelectItem value="in-stock">In Stock</SelectItem>
                      <SelectItem value="low-stock">Low Stock</SelectItem>
                      <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-2 justify-end">
                <Button variant="outline" onClick={handleGenerateReport}>
                  Generate Report
                </Button>
                <Button onClick={handleDownloadReport}>
                  <Download className="mr-2 h-4 w-4" />
                  Download Report
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Inventory Status</CardTitle>
              <CardDescription>
                Current inventory levels and status.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingInventory ? (
                <div className="flex justify-center p-4">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : inventoryData.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  No inventory data available for the selected category.
                </div>
              ) : (
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="h-12 px-4 text-left align-middle font-medium">Product</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">SKU</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Category</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">In Stock</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Price</th>
                      </tr>
                    </thead>
                    <tbody>
                      {inventoryData.map((product: any) => (
                        <tr key={product.id} className="border-b">
                          <td className="p-4 align-middle">{product.name}</td>
                          <td className="p-4 align-middle">{product.sku || "N/A"}</td>
                          <td className="p-4 align-middle">{product.category || "Uncategorized"}</td>
                          <td className="p-4 align-middle">
                            <span 
                              className={cn(
                                "px-2 py-1 rounded-full text-xs font-medium",
                                product.stock <= 0 
                                  ? "bg-red-100 text-red-800" 
                                  : product.stock < 5 
                                    ? "bg-yellow-100 text-yellow-800" 
                                    : "bg-green-100 text-green-800"
                              )}
                            >
                              {product.stock || 0}
                            </span>
                          </td>
                          <td className="p-4 align-middle">Tsh {product.price?.toLocaleString() || "0"}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
